package org.jeecg.modules.data.spatiotemporal.service.impl;

import io.minio.ListObjectsArgs;
import io.minio.MinioClient;
import io.minio.Result;
import io.minio.messages.Item;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.data.spatiotemporal.entity.FlightInspectionImage;
import org.jeecg.modules.data.spatiotemporal.mapper.FlightInspectionImageMapper;
import org.jeecg.modules.data.spatiotemporal.service.IMinioSyncService;
import org.jeecg.modules.flight.service.IFlightRecordsService;
import org.jeecg.modules.flight.entity.FlightRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MinIO同步服务实现类
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
@Slf4j
@Service
public class MinioSyncServiceImpl implements IMinioSyncService {

    private MinioClient minioClient;

    @Autowired
    @Qualifier("flightInspectionImageMapperPrimary")
    private FlightInspectionImageMapper flightInspectionImageMapper;

    @Autowired
    private IFlightRecordsService flightRecordsService;

    @Value("${jeecg.minio.bucketName:djicloudapi}")
    private String defaultBucketName;

    @Value("${jeecg.minio.minio_url:http://172.16.199.153:9000}")
    private String minioUrl;

    @Value("${jeecg.minio.minio_name:djiacc}")
    private String minioName;

    @Value("${jeecg.minio.minio_pass:di^jT2cD*2g}")
    private String minioPass;

    private static final Pattern TASK_ID_PATTERN = Pattern.compile("/([0-9]{10,})/");

    @PostConstruct
    public void init() {
        try {
            String endpoint = StringUtils.isBlank(minioUrl) ? "http://172.16.199.153:9000" : minioUrl;
            String accessKey = StringUtils.isBlank(minioName) ? "djiacc" : minioName;
            String secretKey = StringUtils.isBlank(minioPass) ? "di^jT2cD*2g" : minioPass;

            this.minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();
        } catch (Exception e) {
            log.error("MinioClient初始化失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Integer> syncImagesFromMinio(String bucketName, String taskId) {
        return syncImagesFromMinio(bucketName, taskId, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Integer> syncImagesFromMinio(String bucketName, String taskId, Map<String, Object> defaultConfig) {
        final String bucket = StringUtils.isNotBlank(bucketName) ? bucketName : defaultBucketName;

        AtomicInteger inserted = new AtomicInteger(0);
        AtomicInteger updated = new AtomicInteger(0);
        AtomicInteger skipped = new AtomicInteger(0);
        AtomicInteger errors = new AtomicInteger(0);

        Map<String, Double> defaultLocation = getDefaultLocation(defaultConfig);
        String defaultCreator = getConfigValue(defaultConfig, "creator", "system");
        String defaultOrgCode = getConfigValue(defaultConfig, "orgCode", "A01");

        boolean hasTaskId = StringUtils.isNotBlank(taskId);
        if (hasTaskId && !taskId.trim().matches("^\\d+$")) {
            throw new RuntimeException("任务ID格式无效，必须是纯数字格式");
        }

        try {
            validateMinioConnection(bucket);

            List<Result<Item>> allResults = collectMinioObjects(bucket, hasTaskId, taskId);


            processMinioObjects(allResults, hasTaskId, taskId, defaultLocation, defaultCreator, defaultOrgCode,
                               inserted, updated, skipped, errors);
            if (hasTaskId && StringUtils.isNotBlank(taskId)) {
                updateFlightRecordsPhotoStatus(taskId, inserted.get(), updated.get(), errors.get());
            }

            Map<String, Integer> resultMap = new HashMap<>();
            resultMap.put("inserted", inserted.get());
            resultMap.put("updated", updated.get());
            resultMap.put("skipped", skipped.get());
            resultMap.put("errors", errors.get());
            return resultMap;
        } catch (Exception e) {
            throw new RuntimeException("同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取MinIO桶中的图片总数
     *
     * @param bucketName 桶名称，可选，未提供则使用默认配置的桶
     * @param taskId 任务ID，可选，用于只统计特定任务ID文件夹下的图片
     * @return 图片总数
     */
    @Override
    public int getImageCountFromMinio(String bucketName, String taskId) {
        final String bucket = StringUtils.isNotBlank(bucketName) ? bucketName : defaultBucketName;
        AtomicInteger count = new AtomicInteger(0);

        try {
            ListObjectsArgs.Builder listArgsBuilder = ListObjectsArgs.builder()
                    .bucket(bucket)
                    .recursive(true);

            if (StringUtils.isNotBlank(taskId)) {
                listArgsBuilder.prefix(taskId);
            }

            Iterable<Result<Item>> results = minioClient.listObjects(listArgsBuilder.build());

            results.forEach(result -> {
                try {
                    Item item = result.get();
                    if (!item.isDir() && isImageFile(item.objectName())) {
                        count.incrementAndGet();
                    }
                } catch (Exception e) {
                    // ignore
                }
            });

            return count.get();
        } catch (Exception e) {
            return 0;
        }
    }

    private boolean isImageFile(String filename) {
        if (StringUtils.isBlank(filename)) {
            return false;
        }

        String lowerFilename = filename.toLowerCase();
        return lowerFilename.endsWith(".jpg") ||
                lowerFilename.endsWith(".jpeg") ||
                lowerFilename.endsWith(".png") ||
                lowerFilename.endsWith(".gif") ||
                lowerFilename.endsWith(".bmp");
    }

    private String extractFileName(String path) {
        if (StringUtils.isBlank(path)) {
            return "";
        }

        int lastSeparator = path.lastIndexOf('/');
        return lastSeparator >= 0 ? path.substring(lastSeparator + 1) : path;
    }

    private FlightInspectionImage findImageByStoragePath(String storagePath) {
        try {
            // 这里使用自定义的查询方法，你可能需要在Mapper中添加相应的方法
            // 或者使用MyBatis-Plus提供的条件构造器
            // 实现根据storagePath查询记录的功能
            return flightInspectionImageMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<FlightInspectionImage>()
                            .eq(FlightInspectionImage::getStoragePath, storagePath));
        } catch (Exception e) {
            return null;
        }
    }

    private String extractTaskId(String storagePath) {
        if (StringUtils.isBlank(storagePath)) {
            return "";
        }

        String normalizedPath = storagePath.startsWith("/") ? storagePath.substring(1) : storagePath;

        if (normalizedPath.contains("wayline/")) {
            String[] parts = normalizedPath.split("/");
            if (parts.length >= 3 && "wayline".equals(parts[0])) {
                String folderName = parts[2];
                if (folderName.contains("_")) {
                    String[] folderParts = folderName.split("_");
                    if (folderParts.length >= 4) {
                        String lastPart = folderParts[folderParts.length - 1];
                        if (lastPart.matches("^\\d+$")) {
                            return lastPart;
                        }
                    } else if (folderParts.length == 3) {
                        String lastPart = folderParts[2];
                        if (lastPart.matches("^\\d+$")) {
                            return lastPart;
                        }
                    }
                }
            }
        }

        String[] parts = normalizedPath.split("/");
        if (parts.length >= 2) {
            String folderId = parts[0];
            if (folderId.matches("^\\d+$")) {
                return folderId;
            }
        }

        Matcher matcher = TASK_ID_PATTERN.matcher(storagePath);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return "";
    }

    private List<String> findWaylinePathsForTaskId(String bucket, String taskId) {
        List<String> waylinePaths = new ArrayList<>();

        try {
            if (minioClient == null) {
                return waylinePaths;
            }

            try {
                boolean bucketExists = minioClient.bucketExists(
                        io.minio.BucketExistsArgs.builder().bucket(bucket).build()
                );
                if (!bucketExists) {
                    return waylinePaths;
                }
            } catch (Exception e) {
                return waylinePaths;
            }

            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix("wayline/")
                            .recursive(true)
                            .build());

            Set<String> processedPaths = new HashSet<>();

            for (Result<Item> result : results) {
                try {
                    Item item = result.get();
                    if (item.isDir()) {
                        continue;
                    }

                    String objectName = item.objectName();

                    if (objectName.contains(taskId)) {
                        String standardizedPath = standardizeStoragePath(objectName);
                        String extractedTaskId = extractTaskId(standardizedPath);

                        if (taskId.equals(extractedTaskId)) {
                            String folderPath = extractWaylineFolderPath(objectName, taskId);
                            if (folderPath != null && !processedPaths.contains(folderPath)) {
                                waylinePaths.add(folderPath);
                                processedPaths.add(folderPath);
                            }
                        }
                    }
                } catch (Exception e) {
                    // ignore
                }
            }
        } catch (Exception e) {
            log.warn("查找wayline路径失败: " + e.getMessage());
        }

        return waylinePaths;
    }

    private String extractWaylineFolderPath(String objectName, String taskId) {
        if (!objectName.contains("wayline/") || !objectName.contains(taskId)) {
            return null;
        }

        String[] parts = objectName.split("/");

        if (parts.length >= 5 && "djicloudapi".equals(parts[0]) && "wayline".equals(parts[1])) {
            String folderName = parts[3];
            if (folderName.endsWith(taskId)) {
                return parts[0] + "/" + parts[1] + "/" + parts[2] + "/" + parts[3] + "/";
            }
        } else if (parts.length >= 4 && "wayline".equals(parts[0])) {
            String folderName = parts[2];
            if (folderName.endsWith(taskId)) {
                return parts[0] + "/" + parts[1] + "/" + parts[2] + "/";
            }
        }

        return null;
    }

    @Override
    public InputStream getImageStream(String storagePath) {
        if (StringUtils.isBlank(storagePath)) {
            return null;
        }

        try {
            if (minioClient == null) {
                return null;
            }

            String objectPath = storagePath.startsWith("/") ? storagePath.substring(1) : storagePath;

            return minioClient.getObject(
                    io.minio.GetObjectArgs.builder()
                            .bucket(defaultBucketName)
                            .object(objectPath)
                            .build()
            );
        } catch (Exception e) {
            return null;
        }
    }

    private String standardizeStoragePath(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }

        String normalizedPath = path;

        if (normalizedPath.startsWith("djicloudapi/wayline/")) {
            normalizedPath = normalizedPath.substring("djicloudapi/".length());
        } else if (normalizedPath.startsWith("/djicloudapi/wayline/")) {
            normalizedPath = normalizedPath.substring("/djicloudapi/".length());
        }

        if (!normalizedPath.startsWith("/")) {
            normalizedPath = "/" + normalizedPath;
        }

        return normalizedPath;
    }

    private Long generateUnknownTaskId(String storagePath) {
        if (StringUtils.isBlank(storagePath)) {
            return System.currentTimeMillis();
        }

        String parentPath = "";
        int lastSlash = storagePath.lastIndexOf('/');
        if (lastSlash > 0) {
            parentPath = storagePath.substring(0, lastSlash);
        }

        if (StringUtils.isBlank(parentPath) || "/".equals(parentPath)) {
            return 9999999999999999L;
        }

        int hash = parentPath.hashCode();
        long taskId = Math.abs((long) hash) + 1000000000000000L;
        return taskId;
    }

    private Map<String, Double> getDefaultLocation(Map<String, Object> defaultConfig) {
        Map<String, Double> defaultLocation = defaultConfig != null ?
                (Map<String, Double>) defaultConfig.get("defaultLocation") : null;

        if (defaultLocation == null) {
            defaultLocation = new HashMap<>();
            defaultLocation.put("latitude", 25.059399);
            defaultLocation.put("longitude", 102.878423);
        }
        return defaultLocation;
    }

    private String getConfigValue(Map<String, Object> defaultConfig, String key, String defaultValue) {
        return defaultConfig != null ? (String) defaultConfig.get(key) : defaultValue;
    }

    private void validateMinioConnection(String bucket) {
        if (minioClient == null) {
            throw new RuntimeException("MinioClient未初始化");
        }

        try {
            boolean bucketExists = minioClient.bucketExists(
                    io.minio.BucketExistsArgs.builder().bucket(bucket).build()
            );
            if (!bucketExists) {
                minioClient.makeBucket(
                        io.minio.MakeBucketArgs.builder().bucket(bucket).build()
                );
            }
        } catch (Exception e) {
            throw new RuntimeException("无法访问MinIO桶: " + e.getMessage(), e);
        }
    }

    private List<Result<Item>> collectMinioObjects(String bucket, boolean hasTaskId, String taskId) {
        List<Result<Item>> allResults = new ArrayList<>();

        try {
            if (hasTaskId) {
                String taskIdTrimmed = taskId.trim();
                String exactPrefix = taskIdTrimmed.endsWith("/") ? taskIdTrimmed : taskIdTrimmed + "/";
                List<String> waylinePrefixes = findWaylinePathsForTaskId(bucket, taskIdTrimmed);

                validateTaskExists(bucket, exactPrefix, waylinePrefixes, taskId);

                collectFromTraditionalPath(bucket, exactPrefix, allResults);
                collectFromWaylinePaths(bucket, waylinePrefixes, allResults);
            } else {
                collectAllObjects(bucket, allResults);
            }
        } catch (Exception e) {
            throw new RuntimeException("收集MinIO对象失败: " + e.getMessage(), e);
        }

        return allResults;
    }

    private void validateTaskExists(String bucket, String exactPrefix, List<String> waylinePrefixes, String taskId) {
        boolean folderExists = false;

        try {
            Iterable<Result<Item>> checkResults = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix(exactPrefix)
                            .maxKeys(1)
                            .build());

            for (Result<Item> result : checkResults) {
                folderExists = true;
                break;
            }

            if (!folderExists && !waylinePrefixes.isEmpty()) {
                folderExists = true;
            }

            if (!folderExists) {
                throw new RuntimeException("任务ID " + taskId + " 对应的文件夹在MinIO中不存在");
            }
        } catch (RuntimeException re) {
            throw re;
        } catch (Exception e) {
            throw new RuntimeException("无法验证任务ID文件夹是否存在: " + e.getMessage(), e);
        }
    }

    private void collectFromTraditionalPath(String bucket, String exactPrefix, List<Result<Item>> allResults) {
        try {
            Iterable<Result<Item>> traditionalResults = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix(exactPrefix)
                            .recursive(true)
                            .build());

            for (Result<Item> result : traditionalResults) {
                allResults.add(result);
            }
        } catch (Exception e) {
            log.warn("查询传统路径失败: " + e.getMessage());
        }
    }

    private void collectFromWaylinePaths(String bucket, List<String> waylinePrefixes, List<Result<Item>> allResults) {
        for (String waylinePrefix : waylinePrefixes) {
            try {
                Iterable<Result<Item>> waylineResults = minioClient.listObjects(
                        ListObjectsArgs.builder()
                                .bucket(bucket)
                                .prefix(waylinePrefix)
                                .recursive(true)
                                .build());

                for (Result<Item> result : waylineResults) {
                    allResults.add(result);
                }
            } catch (Exception e) {
                log.warn("查询wayline路径失败: " + waylinePrefix + ", " + e.getMessage());
            }
        }
    }

    private void collectAllObjects(String bucket, List<Result<Item>> allResults) {
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .recursive(true)
                            .build());

            for (Result<Item> result : results) {
                allResults.add(result);
            }
        } catch (Exception e) {
            throw new RuntimeException("查询所有对象失败: " + e.getMessage(), e);
        }
    }

    private void processMinioObjects(List<Result<Item>> allResults, boolean hasTaskId, String taskId,
                                   Map<String, Double> defaultLocation, String defaultCreator, String defaultOrgCode,
                                   AtomicInteger inserted, AtomicInteger updated, AtomicInteger skipped, AtomicInteger errors) {

        for (Result<Item> result : allResults) {
            try {
                Item item = result.get();
                if (item.isDir() || !isImageFile(item.objectName())) {
                    skipped.incrementAndGet();
                    continue;
                }

                if (hasTaskId && !validateObjectBelongsToTask(item.objectName(), taskId)) {
                    skipped.incrementAndGet();
                    continue;
                }

                processImageObject(item, defaultLocation, defaultCreator, defaultOrgCode, inserted, updated, errors);

            } catch (Exception e) {
                errors.incrementAndGet();
                log.warn("处理图片对象失败: {}", e.getMessage());
            }
        }
    }

    private boolean validateObjectBelongsToTask(String objectName, String taskId) {
        String extractedTaskId = extractTaskId(objectName);
        return taskId.trim().equals(extractedTaskId);
    }

    private void processImageObject(Item item, Map<String, Double> defaultLocation, String defaultCreator, String defaultOrgCode,
                                  AtomicInteger inserted, AtomicInteger updated, AtomicInteger errors) {
        try {
            String storagePath = standardizeStoragePath(item.objectName());
            String imageName = extractFileName(item.objectName());
            Date captureTime = Date.from(item.lastModified().toInstant());

            FlightInspectionImage existingImage = findImageByStoragePath(storagePath);

            if (existingImage != null) {
                updateExistingImage(existingImage, imageName, captureTime, storagePath);
                updated.incrementAndGet();
            } else {
                createNewImage(storagePath, imageName, captureTime, defaultLocation, defaultCreator, defaultOrgCode);
                inserted.incrementAndGet();
            }
        } catch (Exception e) {
            errors.incrementAndGet();
            log.warn("处理图片对象失败: {}", e.getMessage());
        }
    }

    private void updateExistingImage(FlightInspectionImage existingImage, String imageName, Date captureTime, String storagePath) {
        existingImage.setImageName(imageName);
        existingImage.setCaptureTime(captureTime);
        existingImage.setUpdateTime(new Date());
        existingImage.setUpdateBy("system");

        String extractedTaskId = extractTaskId(storagePath);
        if (StringUtils.isNotBlank(extractedTaskId) && extractedTaskId.matches("^\\d+$")) {
            existingImage.setTaskId(Long.parseLong(extractedTaskId));
        }

        flightInspectionImageMapper.updateById(existingImage);
    }

    private void createNewImage(String storagePath, String imageName, Date captureTime,
                              Map<String, Double> defaultLocation, String defaultCreator, String defaultOrgCode) {
        FlightInspectionImage newImage = new FlightInspectionImage();
        newImage.setStoragePath(storagePath);
        newImage.setImageName(imageName);
        newImage.setCaptureTime(captureTime);
        newImage.setIsAnnotated(0);
        newImage.setCreateTime(new Date());
        newImage.setCreateBy(defaultCreator);
        newImage.setSysOrgCode(defaultOrgCode);

        String extractedTaskId = extractTaskId(storagePath);
        if (StringUtils.isNotBlank(extractedTaskId) && extractedTaskId.matches("^\\d+$")) {
            newImage.setTaskId(Long.parseLong(extractedTaskId));
        } else {
            newImage.setTaskId(generateUnknownTaskId(storagePath));
        }

        setImageLocation(newImage, defaultLocation);
        flightInspectionImageMapper.insert(newImage);
    }

    private void setImageLocation(FlightInspectionImage newImage, Map<String, Double> defaultLocation) {
        try {
            double lng = defaultLocation.get("longitude");
            double lat = defaultLocation.get("latitude");

            double randomOffset = (Math.random() - 0.5) * 0.0006;
            lng += randomOffset;
            lat += randomOffset;

            String geoJson = String.format(
                    "{\"type\":\"Point\",\"coordinates\":[%f,%f]}",
                    lng, lat
            );
            newImage.setLocation(geoJson);
        } catch (Exception e) {
            log.warn("设置图片位置失败: {}", e.getMessage());
        }
    }

    private void updateFlightRecordsPhotoStatus(String taskId, int inserted, int updated, int errors) {
        try {
            long taskIdLong = Long.parseLong(taskId);

            FlightRecords.PhotoStatusEnum status;
            if (errors > 0) {
                status = FlightRecords.PhotoStatusEnum.FAILED;
            } else if (inserted > 0 || updated > 0) {
                status = FlightRecords.PhotoStatusEnum.COMPLETED;
            } else {
                status = FlightRecords.PhotoStatusEnum.NOT_SYNCED;
            }

            int totalPhotoNums = inserted + updated;

            log.info("准备更新飞行记录照片状态: taskId={}, status={}, 新增={}, 更新={}, 错误={}, 总数={}",
                    taskIdLong, status, inserted, updated, errors, totalPhotoNums);

            boolean updateResult = flightRecordsService.updatePhotoStatus(
                    taskIdLong, status, inserted, updated, errors,
                    totalPhotoNums > 0 ? totalPhotoNums : null);

            if (updateResult) {
                log.info("飞行记录照片状态更新成功: taskId={}, status={}", taskIdLong, status);
            } else {
                log.warn("飞行记录照片状态更新失败: taskId={}", taskIdLong);
            }
        } catch (Exception e) {
            log.error("更新飞行记录照片状态失败: taskId={}", taskId, e);
        }
    }
}